#ifndef SEARCH_H
#define SEARCH_H

#include "types.h"
#include "position.h"
#include <chrono>
#include <atomic>

// Forward declarations
struct SearchInfo;
struct SearchLimits;

// Search result structure
struct SearchResult {
    Move bestMove;
    Value score;
    Depth depth;
    uint64_t nodes;
    int time;
    std::string pv; // Principal variation
};

// Search limits structure
struct SearchLimits {
    int time[2];        // Time left for each side
    int inc[2];         // Time increment per move
    int movestogo;      // Moves to go until next time control
    int depth;          // Maximum search depth
    int movetime;       // Exact time to search
    uint64_t nodes;     // Maximum nodes to search
    bool infinite;      // Search until stopped
    bool ponder;        // Pondering mode
    
    SearchLimits() {
        time[WHITE] = time[BLACK] = 0;
        inc[WHITE] = inc[BLACK] = 0;
        movestogo = depth = movetime = 0;
        nodes = 0;
        infinite = ponder = false;
    }
};

// Search information structure
struct SearchInfo {
    std::chrono::steady_clock::time_point startTime;
    std::atomic<bool> stop;
    std::atomic<uint64_t> nodes;
    SearchLimits limits;
    
    SearchInfo() : stop(false), nodes(0) {}
    
    void clear() {
        stop = false;
        nodes = 0;
        startTime = std::chrono::steady_clock::now();
    }
    
    int elapsed() const {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime).count();
    }
    
    bool should_stop() const {
        if (stop.load())
            return true;
        
        if (limits.movetime && elapsed() >= limits.movetime)
            return true;
        
        if (limits.nodes && nodes.load() >= limits.nodes)
            return true;
        
        return false;
    }
};

// Transposition table entry
struct TTEntry {
    uint64_t key;
    Move move;
    Value value;
    Value eval;
    Depth depth;
    uint8_t bound;
    uint8_t generation;
};

// Transposition table bounds
enum Bound {
    BOUND_NONE,
    BOUND_UPPER,
    BOUND_LOWER,
    BOUND_EXACT
};

// Transposition table class
class TranspositionTable {
public:
    TranspositionTable(size_t mbSize = 16);
    ~TranspositionTable();
    
    void clear();
    void new_search();
    TTEntry* probe(uint64_t key, bool& found);
    void store(uint64_t key, Move move, Value value, Value eval, Depth depth, Bound bound);
    int hashfull() const;
    
private:
    TTEntry* table;
    size_t size;
    uint8_t generation;
};

// Search class
class Search {
public:
    Search();
    ~Search();
    
    SearchResult think(Position& pos, const SearchLimits& limits);
    void stop();
    void clear();
    
    // Search statistics
    uint64_t nodes() const { return info.nodes.load(); }
    int time() const { return info.elapsed(); }
    
private:
    SearchInfo info;
    TranspositionTable tt;
    
    // Main search functions
    Value search(Position& pos, Value alpha, Value beta, Depth depth, int ply);
    Value qsearch(Position& pos, Value alpha, Value beta, int ply);
    
    // Search helpers
    Value search_root(Position& pos, Value alpha, Value beta, Depth depth);
    bool is_repetition(const Position& pos, int ply);
    void update_pv(Move move, int ply);
    
    // Move ordering
    void order_moves(const Position& pos, Move* begin, Move* end, Move ttMove, int ply);
    Value move_value(const Position& pos, Move m, Move ttMove);
    
    // Time management
    bool check_time();
    int time_for_move(Color us) const;
    
    // Principal variation
    Move pv[64][64];
    int pvLength[64];
    
    // Search tables
    Move killers[64][2];
    int history[2][64][64];
    
    // Search constants
    static constexpr int MAX_PLY = 64;
    static constexpr Value MATE_IN_MAX_PLY = VALUE_MATE - MAX_PLY;
    static constexpr Value MATED_IN_MAX_PLY = -VALUE_MATE + MAX_PLY;
};

// Global search instance
extern Search search;

// Search utility functions
inline bool is_mate_score(Value v) {
    return std::abs(v) >= MATE_IN_MAX_PLY;
}

inline Value mate_in(int ply) {
    return VALUE_MATE - ply;
}

inline Value mated_in(int ply) {
    return -VALUE_MATE + ply;
}

inline int mate_distance(Value v) {
    return v > 0 ? VALUE_MATE - v : -VALUE_MATE - v;
}

// Move ordering values
constexpr Value MVV_LVA[PIECE_TYPE_NB][PIECE_TYPE_NB] = {
    {0, 0, 0, 0, 0, 0, 0},
    {0, 105, 205, 305, 405, 505, 605}, // Pawn captures
    {0, 104, 204, 304, 404, 504, 604}, // Knight captures
    {0, 103, 203, 303, 403, 503, 603}, // Bishop captures
    {0, 102, 202, 302, 402, 502, 602}, // Rook captures
    {0, 101, 201, 301, 401, 501, 601}, // Queen captures
    {0, 100, 200, 300, 400, 500, 600}  // King captures
};

#endif // SEARCH_H
