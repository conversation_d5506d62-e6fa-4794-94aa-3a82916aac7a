#ifndef UCI_H
#define UCI_H

#include "types.h"
#include "position.h"
#include "search.h"
#include <string>
#include <vector>
#include <map>

// UCI option types
enum OptionType {
    OPTION_CHECK,
    OPTION_SPIN,
    OPTION_COMBO,
    OPTION_BUTTON,
    OPTION_STRING
};

// UCI option class
class UCIOption {
public:
    UCIOption() = default;
    UCIOption(bool defaultValue);
    UCIOption(int defaultValue, int minValue, int maxValue);
    UCIOption(const std::string& defaultValue, const std::vector<std::string>& values = {});
    
    UCIOption& operator=(const std::string& value);
    operator bool() const;
    operator int() const;
    operator std::string() const;
    
    std::string to_uci() const;
    
private:
    OptionType type;
    std::string defaultValue;
    std::string currentValue;
    int minValue, maxValue;
    std::vector<std::string> comboValues;
};

// UCI options map
extern std::map<std::string, UCIOption> Options;

// UCI class for handling protocol communication
class UCI {
public:
    UCI();
    void loop();
    
private:
    Position pos;
    StateInfo states[1024];
    int statesPtr;
    
    // UCI command handlers
    void cmd_uci();
    void cmd_debug(const std::string& args);
    void cmd_isready();
    void cmd_setoption(const std::string& args);
    void cmd_register(const std::string& args);
    void cmd_ucinewgame();
    void cmd_position(const std::string& args);
    void cmd_go(const std::string& args);
    void cmd_stop();
    void cmd_ponderhit();
    void cmd_quit();
    
    // Helper functions
    std::vector<std::string> split(const std::string& str, char delimiter = ' ');
    Move parse_move(const std::string& moveStr);
    std::string move_to_string(Move m);
    void set_position(const std::string& fen, const std::vector<std::string>& moves);
    SearchLimits parse_go_command(const std::vector<std::string>& tokens);
    
    // State
    bool debug;
    bool quit;
};

// Global UCI instance
extern UCI uci;

// UCI utility functions
void init_uci();
std::string engine_info();
Move move_from_uci(const Position& pos, const std::string& str);
std::string move_to_uci(Move m);

// Engine information
constexpr const char* ENGINE_NAME = "ChessEngine";
constexpr const char* ENGINE_VERSION = "1.0";
constexpr const char* ENGINE_AUTHOR = "Chess Engine Developer";

#endif // UCI_H
